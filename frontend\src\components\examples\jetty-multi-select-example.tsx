'use client'

import * as React from 'react'
import { JettyMultiSelect } from '@/components/ui/jetty-multi-select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import type { JettyDto } from '@/client/types.gen'

export function JettyMultiSelectExample() {
  const [selectedJettyIds, setSelectedJettyIds] = React.useState<string[]>([])
  const [selectedJetties, setSelectedJetties] = React.useState<JettyDto[]>([])
  const [mode, setMode] = React.useState<'single' | 'multiple'>('multiple')

  // Example: Initial jetty IDs for edit mode (you would get these from your form data)
  const [initialJettyIds] = React.useState<string[]>([
    // Add some initial jetty IDs here if you want to test edit mode
    // 'jetty-id-1', 'jetty-id-2'
  ])

  const handleJettyChange = (jettyIds: string[]) => {
    setSelectedJettyIds(jettyIds)
    console.log('Selected jetty IDs:', jettyIds)
  }

  const handleJettySelectionChange = (jetties: JettyDto[]) => {
    setSelectedJetties(jetties)
    console.log('Selected jetties:', jetties)
  }

  const clearSelection = () => {
    setSelectedJettyIds([])
    setSelectedJetties([])
  }

  const toggleMode = () => {
    setMode(prev => prev === 'single' ? 'multiple' : 'single')
    // Clear selection when changing mode
    setSelectedJettyIds([])
    setSelectedJetties([])
  }

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Jetty Multi-Select Example</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={toggleMode} variant="outline">
              Mode: {mode}
            </Button>
            <Button onClick={clearSelection} variant="outline">
              Clear Selection
            </Button>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Select Jetties:</label>
            <JettyMultiSelect
              value={selectedJettyIds}
              onChange={handleJettyChange}
              placeholder={`Select ${mode === 'single' ? 'a jetty' : 'jetties'}...`}
              mode={mode}
              initialJettyIds={initialJettyIds}
              onJettySelectionChange={handleJettySelectionChange}
              showPort={true}
              maxResults={20}
            />
          </div>

          {selectedJettyIds.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Selected Jetty IDs:</h3>
              <div className="flex flex-wrap gap-1">
                {selectedJettyIds.map(id => (
                  <Badge key={id} variant="secondary">
                    {id}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {selectedJetties.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Selected Jetty Details:</h3>
              <div className="space-y-2">
                {selectedJetties.map(jetty => (
                  <div key={jetty.id} className="p-3 border rounded-lg">
                    <div className="font-medium">{jetty.name || jetty.alias}</div>
                    {jetty.port && (
                      <div className="text-sm text-muted-foreground">Port: {jetty.port}</div>
                    )}
                    {jetty.max && (
                      <div className="text-sm text-muted-foreground">Max Capacity: {jetty.max}</div>
                    )}
                    <div className="text-xs text-muted-foreground">ID: {jetty.id}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p><strong>Features:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Search jetties by name, alias, or port</li>
            <li>Single or multiple selection modes</li>
            <li>Loading states with skeleton UI</li>
            <li>Support for initial values (edit mode)</li>
            <li>Callback for getting full jetty objects</li>
            <li>Debounced search to reduce API calls</li>
            <li>Port information display</li>
          </ul>
          
          <p className="mt-4"><strong>Props:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li><code>value</code>: Array of selected jetty IDs</li>
            <li><code>onChange</code>: Callback when selection changes</li>
            <li><code>mode</code>: 'single' or 'multiple' selection</li>
            <li><code>initialJettyIds</code>: For edit mode, loads specific jetties</li>
            <li><code>onJettySelectionChange</code>: Get full jetty objects</li>
            <li><code>showPort</code>: Show port information in descriptions</li>
            <li><code>maxResults</code>: Limit number of results from API</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
