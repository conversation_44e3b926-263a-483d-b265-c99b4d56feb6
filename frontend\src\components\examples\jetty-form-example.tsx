'use client'

import type { JettyDto } from '@/client/types.gen'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { JettyMultiSelect } from '@/components/ui/jetty-multi-select'
import { Label } from '@/components/ui/label'
import { toast } from '@/lib/useToast'
import * as React from 'react'
import { useForm } from 'react-hook-form'

interface FormData {
  requestName: string
  jettyIds: string[]
  description: string
}

interface JettyFormExampleProps {
  // For edit mode - pass existing data
  initialData?: {
    requestName?: string
    jettyIds?: string[]
    description?: string
  }
  onSubmit?: (data: FormData & { selectedJetties: JettyDto[] }) => void
}

export function JettyFormExample({ initialData, onSubmit }: JettyFormExampleProps) {
  const [selectedJetties, setSelectedJetties] = React.useState<JettyDto[]>([])
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<FormData>({
    defaultValues: {
      requestName: initialData?.requestName || '',
      jettyIds: initialData?.jettyIds || [],
      description: initialData?.description || ''
    }
  })

  const jettyIds = watch('jettyIds')

  const handleJettyChange = (newJettyIds: string[]) => {
    setValue('jettyIds', newJettyIds)
  }

  const handleJettySelectionChange = (jetties: JettyDto[]) => {
    setSelectedJetties(jetties)
  }

  const onFormSubmit = (data: FormData) => {
    if (data.jettyIds.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Please select at least one jetty',
        variant: 'destructive'
      })
      return
    }

    const submitData = {
      ...data,
      selectedJetties
    }

    console.log('Form submitted:', submitData)
    
    if (onSubmit) {
      onSubmit(submitData)
    } else {
      toast({
        title: 'Form Submitted',
        description: `Request "${data.requestName}" with ${data.jettyIds.length} jetty(s) selected`,
      })
    }
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>
          {initialData ? 'Edit Jetty Request' : 'Create Jetty Request'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="requestName">Request Name *</Label>
            <Input
              id="requestName"
              {...register('requestName', { 
                required: 'Request name is required',
                minLength: { value: 3, message: 'Request name must be at least 3 characters' }
              })}
              placeholder="Enter request name"
            />
            {errors.requestName && (
              <p className="text-sm text-destructive">{errors.requestName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label>Select Jetties *</Label>
            <JettyMultiSelect
              value={jettyIds}
              onChange={handleJettyChange}
              placeholder="Select jetties for this request..."
              mode="multiple"
              initialJettyIds={initialData?.jettyIds}
              onJettySelectionChange={handleJettySelectionChange}
              showPort={true}
              maxResults={30}
            />
            {jettyIds.length === 0 && (
              <p className="text-sm text-muted-foreground">
                Please select at least one jetty
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              {...register('description')}
              placeholder="Enter description (optional)"
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>

          {selectedJetties.length > 0 && (
            <div className="space-y-2">
              <Label>Selected Jetties ({selectedJetties.length})</Label>
              <div className="grid gap-2 max-h-40 overflow-y-auto">
                {selectedJetties.map(jetty => (
                  <div key={jetty.id} className="flex items-center justify-between p-2 bg-muted rounded">
                    <div>
                      <div className="font-medium text-sm">{jetty.name || jetty.alias}</div>
                      {jetty.port && (
                        <div className="text-xs text-muted-foreground">Port: {jetty.port}</div>
                      )}
                    </div>
                    {jetty.max && (
                      <div className="text-xs text-muted-foreground">
                        Max: {jetty.max}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-2">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Submitting...' : (initialData ? 'Update Request' : 'Create Request')}
            </Button>
            <Button type="button" variant="outline" onClick={() => window.location.reload()}>
              Reset
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

// Example usage component
export function JettyFormExampleUsage() {
  const [mode, setMode] = React.useState<'create' | 'edit'>('create')
  
  // Example initial data for edit mode
  const editData = {
    requestName: 'Sample Jetty Request',
    jettyIds: [], // You would populate this with actual jetty IDs
    description: 'This is a sample request for testing edit mode'
  }

  const handleSubmit = (data: FormData & { selectedJetties: JettyDto[] }) => {
    console.log('Received form data:', data)
    toast({
      title: 'Success',
      description: `${mode === 'create' ? 'Created' : 'Updated'} request with ${data.selectedJetties.length} jetties`
    })
  }

  return (
    <div className="space-y-4 p-6">
      <div className="flex gap-2">
        <Button 
          onClick={() => setMode('create')} 
          variant={mode === 'create' ? 'default' : 'outline'}
        >
          Create Mode
        </Button>
        <Button 
          onClick={() => setMode('edit')} 
          variant={mode === 'edit' ? 'default' : 'outline'}
        >
          Edit Mode
        </Button>
      </div>
      
      <JettyFormExample
        key={mode} // Force re-render when mode changes
        initialData={mode === 'edit' ? editData : undefined}
        onSubmit={handleSubmit}
      />
    </div>
  )
}
