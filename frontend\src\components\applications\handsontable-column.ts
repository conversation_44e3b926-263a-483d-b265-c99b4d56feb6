export const columns = [
  { data: 'tenantName', type: 'text', title: 'Tenant', width: "10" },
  { data: 'itemName', type: 'text', title: 'Item Name', width: "30", wordWrap: false },
  { data: 'quantity', type: 'numeric', title: 'Quantity', width: "8" },
  { data: 'uom', type: 'text', title: 'UoM', width: "8" },
  { data: 'remark', type: 'text', title: 'Remark', width: "15" },
  { data: 'status', type: 'text', title: 'Status', width: "8", readOnly: true },
  { data: 'letterNo', type: 'text', title: 'Letter No', width: "12" },
  { data: 'letterDate', type: 'date', title: 'Letter Date', width: "12" },
  { data: 'id', title: 'Preview', width: "8", readOnly: true, filterable: false },
  { data: 'submit', title: 'Submit', width: "8", readOnly: true, filterable: false },
  { data: 'delete', title: 'Delete', width: "8", readOnly: true, filterable: false },
];

export type TableRowData = {
  id?: string; // JettyRequestItem ID for document generation
  tenantName: string;
  itemName: string;
  quantity: string;
  uom: string;
  remark: string;
  status: string;
  letterNo: string;
  letterDate: string;
  preview?: string;
  submit: string;
  delete: string;
};