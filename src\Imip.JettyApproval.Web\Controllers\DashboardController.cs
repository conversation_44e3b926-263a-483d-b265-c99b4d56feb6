using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;
using Imip.JettyApproval.JettyRequests;
using Imip.JettyApproval.Approvals.ApprovalStages;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;
using System.Threading.Tasks;
using System.Linq;
using System;

namespace Imip.JettyApproval.Web.Controllers;

[ApiController]
[Route("api/dashboard")]
[Authorize]
public class DashboardController : AbpController
{
    private readonly IJettyRequestAppService _jettyRequestAppService;
    private readonly IApprovalStageAppService _approvalStageAppService;

    public DashboardController(
        IJettyRequestAppService jettyRequestAppService,
        IApprovalStageAppService approvalStageAppService)
    {
        _jettyRequestAppService = jettyRequestAppService;
        _approvalStageAppService = approvalStageAppService;
    }

    [HttpGet("statistics")]
    public async Task<IActionResult> GetDashboardStatistics()
    {
        try
        {
            // Get total approval requests (jetty requests)
            var totalRequestsQuery = new QueryParametersDto
            {
                MaxResultCount = 1,
                SkipCount = 0
            };
            var totalRequestsResult = await _jettyRequestAppService.FilterListAsync(totalRequestsQuery);
            var totalApprovalRequests = totalRequestsResult.TotalCount;

            // Get pending approvals (approval stages with status "Pending")
            var pendingApprovalsQuery = new QueryParametersDto
            {
                MaxResultCount = 1,
                SkipCount = 0,
                FilterGroup = new FilterGroup
                {
                    Logic = FilterLogic.And,
                    Filters = new[]
                    {
                        new FilterDescriptor
                        {
                            Field = "Status",
                            Operator = FilterOperator.Equal,
                            Value = "Pending"
                        }
                    }
                }
            };
            var pendingApprovalsResult = await _approvalStageAppService.FilterListAsync(pendingApprovalsQuery);
            var pendingApprovals = pendingApprovalsResult.TotalCount;

            // Get rejected requests (approval stages with status "Rejected")
            var rejectedRequestsQuery = new QueryParametersDto
            {
                MaxResultCount = 1,
                SkipCount = 0,
                FilterGroup = new FilterGroup
                {
                    Logic = FilterLogic.And,
                    Filters = new[]
                    {
                        new FilterDescriptor
                        {
                            Field = "Status",
                            Operator = FilterOperator.Equal,
                            Value = "Rejected"
                        }
                    }
                }
            };
            var rejectedRequestsResult = await _approvalStageAppService.FilterListAsync(rejectedRequestsQuery);
            var rejectedRequests = rejectedRequestsResult.TotalCount;

            // Get scheduled vessels today (jetty requests with arrival date today)
            var today = DateTime.Today;
            var scheduledVesselsQuery = new QueryParametersDto
            {
                MaxResultCount = 1,
                SkipCount = 0,
                FilterGroup = new FilterGroup
                {
                    Logic = FilterLogic.And,
                    Filters = new[]
                    {
                        new FilterDescriptor
                        {
                            Field = "ArrivalDate",
                            Operator = FilterOperator.GreaterThanOrEqual,
                            Value = today.ToString("yyyy-MM-dd")
                        },
                        new FilterDescriptor
                        {
                            Field = "ArrivalDate",
                            Operator = FilterOperator.LessThan,
                            Value = today.AddDays(1).ToString("yyyy-MM-dd")
                        }
                    }
                }
            };
            var scheduledVesselsResult = await _jettyRequestAppService.FilterListAsync(scheduledVesselsQuery);
            var scheduledVesselsToday = scheduledVesselsResult.TotalCount;

            var statistics = new
            {
                TotalApprovalRequests = totalApprovalRequests,
                PendingApprovals = pendingApprovals,
                RejectedRequests = rejectedRequests,
                ScheduledVesselsToday = scheduledVesselsToday,
                LastUpdated = DateTime.UtcNow
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            return BadRequest($"Error retrieving dashboard statistics: {ex.Message}");
        }
    }

    [HttpGet("pending-approvals")]
    public async Task<IActionResult> GetPendingApprovals([FromQuery] int maxResults = 10)
    {
        try
        {
            var query = new QueryParametersDto
            {
                MaxResultCount = maxResults,
                SkipCount = 0,
                FilterGroup = new FilterGroup
                {
                    Logic = FilterLogic.And,
                    Filters = new[]
                    {
                        new FilterDescriptor
                        {
                            Field = "Status",
                            Operator = FilterOperator.Equal,
                            Value = "Pending"
                        }
                    }
                },
                Sorting = "CreationTime desc"
            };

            var result = await _approvalStageAppService.FilterListAsync(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest($"Error retrieving pending approvals: {ex.Message}");
        }
    }

    [HttpGet("jetty-status")]
    public async Task<IActionResult> GetJettyStatus()
    {
        try
        {
            // This is a placeholder for jetty status data
            // In a real implementation, this would fetch actual jetty utilization data
            var jettyStatus = new[]
            {
                new { JettyName = "F1", Occupied = 2, Available = 1 },
                new { JettyName = "F2", Occupied = 3, Available = 2 },
                new { JettyName = "F3", Occupied = 1, Available = 3 },
                new { JettyName = "F4", Occupied = 0, Available = 4 },
                new { JettyName = "F5", Occupied = 2, Available = 2 },
                new { JettyName = "F6", Occupied = 1, Available = 3 }
            };

            return Ok(jettyStatus);
        }
        catch (Exception ex)
        {
            return BadRequest($"Error retrieving jetty status: {ex.Message}");
        }
    }
}
