'use client'

import * as React from 'react'
import { MultiSelect, type MultiSelectOption } from './multi-select'
import { useJettyDataWithFilter } from '@/lib/hooks/useJettyDataWithFilter'
import { useDebounce } from '@/lib/hooks/useDebounce'
import type { JettyDto, FilterRequestDto } from '@/client/types.gen'
import { type FieldValues, type Path, type UseFormRegister } from "react-hook-form"

type JettyMultiSelectProps<T extends FieldValues = FieldValues> = {
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  maxHeight?: number
  mode?: 'multiple' | 'single'
  name?: Path<T>
  register?: UseFormRegister<T>
  valueAsNumber?: boolean
  // Jetty-specific props
  initialJettyIds?: string[] // For loading initial jetty data when editing
  onJettySelectionChange?: (selectedJetties: JettyDto[]) => void
  showPort?: boolean
  maxResults?: number
}

export const JettyMultiSelect = <T extends FieldValues = FieldValues>({
  value = [],
  onChange,
  placeholder = 'Select jetties...',
  className,
  disabled = false,
  maxHeight = 300,
  mode = 'multiple',
  name,
  register,
  valueAsNumber = false,
  initialJettyIds = [],
  onJettySelectionChange,
  showPort = true,
  maxResults = 50,
}: JettyMultiSelectProps<T>) => {
  const [searchValue, setSearchValue] = React.useState('')
  const [allJetties, setAllJetties] = React.useState<JettyDto[]>([])
  const [isInitialLoaded, setIsInitialLoaded] = React.useState(false)

  // Debounce search to avoid too many API calls
  const debouncedSearchValue = useDebounce(searchValue, 300)

  // Hook for fetching jetty data with filter
  const { mutate: fetchJettyData, isPending: isLoading } = useJettyDataWithFilter()

  // Hook for fetching initial jetty data (for edit mode)
  const { mutate: fetchInitialJetties, isPending: isLoadingInitial } = useJettyDataWithFilter()

  // Load initial jetty data when component mounts (for edit mode)
  React.useEffect(() => {
    if (initialJettyIds.length > 0 && !isInitialLoaded) {
      const filterRequest: FilterRequestDto = {
        maxResultCount: initialJettyIds.length,
        skipCount: 0,
        filterGroup: {
          operator: 'Or',
          conditions: initialJettyIds.map(id => ({
            fieldName: 'id',
            operator: 'Equals',
            value: id
          }))
        }
      }

      fetchInitialJetties(filterRequest, {
        onSuccess: (data) => {
          setAllJetties(prev => {
            const existingIds = new Set(prev.map(j => j.id))
            const newJetties = data.filter(j => !existingIds.has(j.id))
            return [...prev, ...newJetties]
          })
          setIsInitialLoaded(true)
        }
      })
    } else if (initialJettyIds.length === 0) {
      setIsInitialLoaded(true)
    }
  }, [initialJettyIds, isInitialLoaded, fetchInitialJetties])

  // Fetch jetty data based on search
  React.useEffect(() => {
    if (isInitialLoaded) {
      const filterRequest: FilterRequestDto = {
        maxResultCount: maxResults,
        skipCount: 0,
        filterGroup: debouncedSearchValue ? {
          operator: 'Or',
          conditions: [
            {
              fieldName: 'name',
              operator: 'Contains',
              value: debouncedSearchValue
            },
            {
              fieldName: 'alias',
              operator: 'Contains',
              value: debouncedSearchValue
            },
            {
              fieldName: 'port',
              operator: 'Contains',
              value: debouncedSearchValue
            }
          ]
        } : undefined
      }

      fetchJettyData(filterRequest, {
        onSuccess: (data) => {
          if (debouncedSearchValue) {
            // For search results, replace the current list
            setAllJetties(data)
          } else {
            // For initial load, merge with existing data
            setAllJetties(prev => {
              const existingIds = new Set(prev.map(j => j.id))
              const newJetties = data.filter(j => !existingIds.has(j.id))
              return [...prev, ...newJetties]
            })
          }
        }
      })
    }
  }, [debouncedSearchValue, isInitialLoaded, fetchJettyData, maxResults])

  // Convert jetty data to MultiSelect options
  const options: MultiSelectOption[] = React.useMemo(() => {
    return allJetties.map(jetty => ({
      value: jetty.id || '',
      label: jetty.name || jetty.alias || 'Unknown Jetty',
      description: showPort && jetty.port ? `Port: ${jetty.port}` : undefined,
      data: jetty
    }))
  }, [allJetties, showPort])

  // Handle selection change and call jetty-specific callback
  const handleSelectionChange = React.useCallback((selectedOptions: MultiSelectOption[]) => {
    if (onJettySelectionChange) {
      const selectedJetties = selectedOptions.map(option => option.data as JettyDto).filter(Boolean)
      onJettySelectionChange(selectedJetties)
    }
  }, [onJettySelectionChange])

  return (
    <MultiSelect<T>
      options={options}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      maxHeight={maxHeight}
      mode={mode}
      name={name}
      register={register}
      valueAsNumber={valueAsNumber}
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      isLoading={isLoading || isLoadingInitial}
      loadingText="Loading jetties..."
      emptyText="No jetties found"
      showDescription={showPort}
      onSelectionChange={handleSelectionChange}
    />
  )
}

export default JettyMultiSelect
