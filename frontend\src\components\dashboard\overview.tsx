import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardAction,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useDashboardStatistics } from '@/lib/hooks/useDashboardStatistics';
import { IconTrendingDown, IconTrendingUp } from '@tabler/icons-react';
import JettyStatus from './jetty-status-new';
import WaitingApproval from './waiting-approval';

export default function OverViewPage() {
  const { data: statistics, isLoading, error } = useDashboardStatistics();
  return (
    <div className='flex flex-1 flex-col space-y-2'>
      <div className='flex items-center justify-between space-y-2'>
        <h2 className='text-2xl font-bold tracking-tight'>
          Hi, Welcome back 👋
        </h2>
      </div>
      <div className='*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4'>
        <Card className='@container/card'>
          <CardHeader>
            <CardDescription>Approval Requests</CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : error ? (
                'Error'
              ) : (
                statistics?.totalApprovalRequests || 0
              )}
            </CardTitle>
            <CardAction>
              <Badge variant='outline'>
                <IconTrendingUp />
                +1.5%
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              Total approval requests <IconTrendingUp className='size-4' />
            </div>
            <div className='text-muted-foreground'>
              All time requests
            </div>
          </CardFooter>
        </Card>
        <Card className='@container/card'>
          <CardHeader>
            <CardDescription>Pending Approval</CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : error ? (
                'Error'
              ) : (
                statistics?.pendingApprovals || 0
              )}
            </CardTitle>
            <CardAction>
              <Badge variant='outline'>
                <IconTrendingDown />
                -20%
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              Pending approvals <IconTrendingDown className='size-4' />
            </div>
            <div className='text-muted-foreground'>
              Awaiting review
            </div>
          </CardFooter>
        </Card>
        <Card className='@container/card'>
          <CardHeader>
            <CardDescription>Rejected Requests</CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : error ? (
                'Error'
              ) : (
                statistics?.rejectedRequests || 0
              )}
            </CardTitle>
            <CardAction>
              <Badge variant='outline'>
                <IconTrendingUp />
                +12.5%
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              Rejected requests <IconTrendingUp className='size-4' />
            </div>
            <div className='text-muted-foreground'>
              Quality control
            </div>
          </CardFooter>
        </Card>
        <Card className='@container/card'>
          <CardHeader>
            <CardDescription>Scheduled Vessels Today</CardDescription>
            <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : error ? (
                'Error'
              ) : (
                statistics?.scheduledVesselsToday || 0
              )}
            </CardTitle>
            <CardAction>
              <Badge variant='outline'>
                <IconTrendingUp />
                +4.5%
              </Badge>
            </CardAction>
          </CardHeader>
          <CardFooter className='flex-col items-start gap-1.5 text-sm'>
            <div className='line-clamp-1 flex gap-2 font-medium'>
              Vessels scheduled today <IconTrendingUp className='size-4' />
            </div>
            <div className='text-muted-foreground'>
              Today's schedule
            </div>
          </CardFooter>
        </Card>
      </div>
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7'>
        <div className='col-span-4'>
          <WaitingApproval />
        </div>
        <div className='col-span-4 md:col-span-3'>
          <JettyStatus />
        </div>
      </div>
    </div>
  );
}
